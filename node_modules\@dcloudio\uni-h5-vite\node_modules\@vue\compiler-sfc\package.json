{"name": "@vue/compiler-sfc", "version": "3.2.47", "description": "@vue/compiler-sfc", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "types": "dist/compiler-sfc.d.ts", "files": ["dist"], "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.47", "@vue/compiler-dom": "3.2.47", "@vue/compiler-ssr": "3.2.47", "@vue/reactivity-transform": "3.2.47", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "magic-string": "^0.25.7", "source-map": "^0.6.1", "postcss": "^8.1.10"}, "devDependencies": {"@types/estree": "^0.0.48", "@babel/types": "^7.16.0", "@types/lru-cache": "^5.1.0", "pug": "^3.0.1", "sass": "^1.26.9", "@vue/consolidate": "^0.17.3", "hash-sum": "^2.0.0", "lru-cache": "^5.1.1", "merge-source-map": "^1.1.0", "postcss-modules": "^4.0.0", "postcss-selector-parser": "^6.0.4"}}