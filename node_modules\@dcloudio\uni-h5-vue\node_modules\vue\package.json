{"name": "vue", "version": "3.2.47", "description": "The progressive JavaScript framework for building modern web UI.", "main": "index.js", "module": "dist/vue.runtime.esm-bundler.js", "types": "dist/vue.d.ts", "unpkg": "dist/vue.global.js", "jsdelivr": "dist/vue.global.js", "files": ["index.js", "index.mjs", "dist", "compiler-sfc", "server-renderer", "macros.d.ts", "macros-global.d.ts", "ref-macros.d.ts"], "exports": {".": {"types": "./dist/vue.d.ts", "import": {"node": "./index.mjs", "default": "./dist/vue.runtime.esm-bundler.js"}, "require": "./index.js"}, "./server-renderer": {"types": "./server-renderer/index.d.ts", "import": "./server-renderer/index.mjs", "require": "./server-renderer/index.js"}, "./compiler-sfc": {"types": "./compiler-sfc/index.d.ts", "import": "./compiler-sfc/index.mjs", "require": "./compiler-sfc/index.js"}, "./dist/*": "./dist/*", "./package.json": "./package.json", "./macros": "./macros.d.ts", "./macros-global": "./macros-global.d.ts", "./ref-macros": "./ref-macros.d.ts"}, "buildOptions": {"name": "<PERSON><PERSON>", "formats": ["esm-bundler", "esm-bundler-runtime", "cjs", "global", "global-runtime", "esm-browser", "esm-browser-runtime"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/vue#readme", "dependencies": {"@vue/shared": "3.2.47", "@vue/compiler-dom": "3.2.47", "@vue/runtime-dom": "3.2.47", "@vue/compiler-sfc": "3.2.47", "@vue/server-renderer": "3.2.47"}}