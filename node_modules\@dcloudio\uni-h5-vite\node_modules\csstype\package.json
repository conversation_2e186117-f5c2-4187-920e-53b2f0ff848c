{"name": "csstype", "version": "2.6.21", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": "https://github.com/frenic/csstype", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#1e8eedcf98a3c282cc745d274050091fa7602490", "mdn-data": "git+https://github.com/mdn/data.git#fea9f55167435f34fb39912151e1cc4288e7afb7", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"]}