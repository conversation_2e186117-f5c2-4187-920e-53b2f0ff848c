<template>
	<view class="bottom-tabbar">
		<view class="debug-info">BottomTabBar 已加载 - Active: {{ active }}</view>
		<view class="tab-item" v-for="item in tabs" :key="item.key" @click="onTap(item)">
			<view class="icon-wrapper" :class="{ active: isActive(item) }">
				<text class="icon-text">{{ getIconText(item) }}</text>
			</view>
			<text class="tab-text" :style="{ color: isActive(item) ? primaryColor : inactiveColor }">{{ item.text }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'BottomTabBar',
	props: {
		active: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			primaryColor: '#2A5CAA',
			inactiveColor: '#7A7E83',
			tabs: [
				{
					key: 'index',
					text: '首页',
					path: 'pages/index/index'
				},
				{
					key: 'plan',
					text: '计划',
					path: 'pages/plan/plan'
				},
				{
					key: 'profile',
					text: '我的',
					path: 'pages/profile/profile'
				}
			]
		}
	},
	mounted() {
		console.log('BottomTabBar mounted, active:', this.active)
		console.log('BottomTabBar DOM element:', this.$el)
	},
	methods: {
		isActive(item) {
			return item.key === this.active
		},
		getIconText(item) {
			const iconMap = {
				'index': '🏠',
				'plan': '📅',
				'profile': '👤'
			}
			return iconMap[item.key] || '●'
		},
		onTap(item) {
			console.log('Tab clicked:', item.key)
			const current = this.active
			if (current === item.key) return
			
			// 使用 reLaunch 进行页面切换
			uni.reLaunch({ url: `/${item.path}` })
		}
	}
}
</script>

<style scoped>
.bottom-tabbar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 140rpx;
	background-color: #ff0000;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-top: 4rpx solid #000000;
	z-index: 99999;
}

.debug-info {
	color: white;
	font-size: 24rpx;
	margin-bottom: 10rpx;
	background-color: #000000;
	padding: 4rpx 8rpx;
	border-radius: 4rpx;
}

.tab-item {
	flex: 1;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 10rpx 0;
}

.icon-wrapper {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.icon-wrapper.active {
	background-color: #ffff00;
}

.icon-text {
	font-size: 32rpx;
}

.tab-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #ffffff;
}
</style>



