{"version": 3, "file": "magic-string.es.js", "sources": ["../src/BitSet.js", "../src/Chunk.js", "../src/SourceMap.js", "../src/utils/guessIndent.js", "../src/utils/getRelativePath.js", "../src/utils/isObject.js", "../src/utils/getLocator.js", "../src/utils/Mappings.js", "../src/MagicString.js", "../src/Bundle.js"], "sourcesContent": ["export default class BitSet {\n\tconstructor(arg) {\n\t\tthis.bits = arg instanceof BitSet ? arg.bits.slice() : [];\n\t}\n\n\tadd(n) {\n\t\tthis.bits[n >> 5] |= 1 << (n & 31);\n\t}\n\n\thas(n) {\n\t\treturn !!(this.bits[n >> 5] & (1 << (n & 31)));\n\t}\n}\n", "export default class Chunk {\n\tconstructor(start, end, content) {\n\t\tthis.start = start;\n\t\tthis.end = end;\n\t\tthis.original = content;\n\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\n\t\tthis.content = content;\n\t\tthis.storeName = false;\n\t\tthis.edited = false;\n\n\t\t// we make these non-enumerable, for sanity while debugging\n\t\tObject.defineProperties(this, {\n\t\t\tprevious: { writable: true, value: null },\n\t\t\tnext: { writable: true, value: null },\n\t\t});\n\t}\n\n\tappendLeft(content) {\n\t\tthis.outro += content;\n\t}\n\n\tappendRight(content) {\n\t\tthis.intro = this.intro + content;\n\t}\n\n\tclone() {\n\t\tconst chunk = new Chunk(this.start, this.end, this.original);\n\n\t\tchunk.intro = this.intro;\n\t\tchunk.outro = this.outro;\n\t\tchunk.content = this.content;\n\t\tchunk.storeName = this.storeName;\n\t\tchunk.edited = this.edited;\n\n\t\treturn chunk;\n\t}\n\n\tcontains(index) {\n\t\treturn this.start < index && index < this.end;\n\t}\n\n\teachNext(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.next;\n\t\t}\n\t}\n\n\teachPrevious(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.previous;\n\t\t}\n\t}\n\n\tedit(content, storeName, contentOnly) {\n\t\tthis.content = content;\n\t\tif (!contentOnly) {\n\t\t\tthis.intro = '';\n\t\t\tthis.outro = '';\n\t\t}\n\t\tthis.storeName = storeName;\n\n\t\tthis.edited = true;\n\n\t\treturn this;\n\t}\n\n\tprependLeft(content) {\n\t\tthis.outro = content + this.outro;\n\t}\n\n\tprependRight(content) {\n\t\tthis.intro = content + this.intro;\n\t}\n\n\tsplit(index) {\n\t\tconst sliceIndex = index - this.start;\n\n\t\tconst originalBefore = this.original.slice(0, sliceIndex);\n\t\tconst originalAfter = this.original.slice(sliceIndex);\n\n\t\tthis.original = originalBefore;\n\n\t\tconst newChunk = new Chunk(index, this.end, originalAfter);\n\t\tnewChunk.outro = this.outro;\n\t\tthis.outro = '';\n\n\t\tthis.end = index;\n\n\t\tif (this.edited) {\n\t\t\t// TODO is this block necessary?...\n\t\t\tnewChunk.edit('', false);\n\t\t\tthis.content = '';\n\t\t} else {\n\t\t\tthis.content = originalBefore;\n\t\t}\n\n\t\tnewChunk.next = this.next;\n\t\tif (newChunk.next) newChunk.next.previous = newChunk;\n\t\tnewChunk.previous = this;\n\t\tthis.next = newChunk;\n\n\t\treturn newChunk;\n\t}\n\n\ttoString() {\n\t\treturn this.intro + this.content + this.outro;\n\t}\n\n\ttrimEnd(rx) {\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.start + trimmed.length).edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\tif (this.intro.length) return true;\n\t\t}\n\t}\n\n\ttrimStart(rx) {\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.end - trimmed.length);\n\t\t\t\tthis.edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.outro = this.outro.replace(rx, '');\n\t\t\tif (this.outro.length) return true;\n\t\t}\n\t}\n}\n", "import { encode } from 'sourcemap-codec';\n\nlet btoa = () => {\n\tthrow new Error('Unsupported environment: `window.btoa` or `Buffer` should be supported.');\n};\nif (typeof window !== 'undefined' && typeof window.btoa === 'function') {\n\tbtoa = (str) => window.btoa(unescape(encodeURIComponent(str)));\n} else if (typeof Buffer === 'function') {\n\tbtoa = (str) => Buffer.from(str, 'utf-8').toString('base64');\n}\n\nexport default class SourceMap {\n\tconstructor(properties) {\n\t\tthis.version = 3;\n\t\tthis.file = properties.file;\n\t\tthis.sources = properties.sources;\n\t\tthis.sourcesContent = properties.sourcesContent;\n\t\tthis.names = properties.names;\n\t\tthis.mappings = encode(properties.mappings);\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this);\n\t}\n\n\ttoUrl() {\n\t\treturn 'data:application/json;charset=utf-8;base64,' + btoa(this.toString());\n\t}\n}\n", "export default function guessIndent(code) {\n\tconst lines = code.split('\\n');\n\n\tconst tabbed = lines.filter((line) => /^\\t+/.test(line));\n\tconst spaced = lines.filter((line) => /^ {2,}/.test(line));\n\n\tif (tabbed.length === 0 && spaced.length === 0) {\n\t\treturn null;\n\t}\n\n\t// More lines tabbed than spaced? Assume tabs, and\n\t// default to tabs in the case of a tie (or nothing\n\t// to go on)\n\tif (tabbed.length >= spaced.length) {\n\t\treturn '\\t';\n\t}\n\n\t// Otherwise, we need to guess the multiple\n\tconst min = spaced.reduce((previous, current) => {\n\t\tconst numSpaces = /^ +/.exec(current)[0].length;\n\t\treturn Math.min(numSpaces, previous);\n\t}, Infinity);\n\n\treturn new Array(min + 1).join(' ');\n}\n", "export default function getRelativePath(from, to) {\n\tconst fromParts = from.split(/[/\\\\]/);\n\tconst toParts = to.split(/[/\\\\]/);\n\n\tfromParts.pop(); // get dirname\n\n\twhile (fromParts[0] === toParts[0]) {\n\t\tfromParts.shift();\n\t\ttoParts.shift();\n\t}\n\n\tif (fromParts.length) {\n\t\tlet i = fromParts.length;\n\t\twhile (i--) fromParts[i] = '..';\n\t}\n\n\treturn fromParts.concat(toParts).join('/');\n}\n", "const toString = Object.prototype.toString;\n\nexport default function isObject(thing) {\n\treturn toString.call(thing) === '[object Object]';\n}\n", "export default function getLocator(source) {\n\tconst originalLines = source.split('\\n');\n\tconst lineOffsets = [];\n\n\tfor (let i = 0, pos = 0; i < originalLines.length; i++) {\n\t\tlineOffsets.push(pos);\n\t\tpos += originalLines[i].length + 1;\n\t}\n\n\treturn function locate(index) {\n\t\tlet i = 0;\n\t\tlet j = lineOffsets.length;\n\t\twhile (i < j) {\n\t\t\tconst m = (i + j) >> 1;\n\t\t\tif (index < lineOffsets[m]) {\n\t\t\t\tj = m;\n\t\t\t} else {\n\t\t\t\ti = m + 1;\n\t\t\t}\n\t\t}\n\t\tconst line = i - 1;\n\t\tconst column = index - lineOffsets[line];\n\t\treturn { line, column };\n\t};\n}\n", "export default class Mappings {\n\tconstructor(hires) {\n\t\tthis.hires = hires;\n\t\tthis.generatedCodeLine = 0;\n\t\tthis.generatedCodeColumn = 0;\n\t\tthis.raw = [];\n\t\tthis.rawSegments = this.raw[this.generatedCodeLine] = [];\n\t\tthis.pending = null;\n\t}\n\n\taddEdit(sourceIndex, content, loc, nameIndex) {\n\t\tif (content.length) {\n\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\tif (nameIndex >= 0) {\n\t\t\t\tsegment.push(nameIndex);\n\t\t\t}\n\t\t\tthis.rawSegments.push(segment);\n\t\t} else if (this.pending) {\n\t\t\tthis.rawSegments.push(this.pending);\n\t\t}\n\n\t\tthis.advance(content);\n\t\tthis.pending = null;\n\t}\n\n\taddUneditedChunk(sourceIndex, chunk, original, loc, sourcemapLocations) {\n\t\tlet originalCharIndex = chunk.start;\n\t\tlet first = true;\n\n\t\twhile (originalCharIndex < chunk.end) {\n\t\t\tif (this.hires || first || sourcemapLocations.has(originalCharIndex)) {\n\t\t\t\tthis.rawSegments.push([this.generatedCodeColumn, sourceIndex, loc.line, loc.column]);\n\t\t\t}\n\n\t\t\tif (original[originalCharIndex] === '\\n') {\n\t\t\t\tloc.line += 1;\n\t\t\t\tloc.column = 0;\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\t\t\t\tfirst = true;\n\t\t\t} else {\n\t\t\t\tloc.column += 1;\n\t\t\t\tthis.generatedCodeColumn += 1;\n\t\t\t\tfirst = false;\n\t\t\t}\n\n\t\t\toriginalCharIndex += 1;\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\tadvance(str) {\n\t\tif (!str) return;\n\n\t\tconst lines = str.split('\\n');\n\n\t\tif (lines.length > 1) {\n\t\t\tfor (let i = 0; i < lines.length - 1; i++) {\n\t\t\t\tthis.generatedCodeLine++;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t}\n\t\t\tthis.generatedCodeColumn = 0;\n\t\t}\n\n\t\tthis.generatedCodeColumn += lines[lines.length - 1].length;\n\t}\n}\n", "import BitSet from './BitSet.js';\nimport Chunk from './Chunk.js';\nimport SourceMap from './SourceMap.js';\nimport guessIndent from './utils/guessIndent.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\nimport Stats from './utils/Stats.js';\n\nconst n = '\\n';\n\nconst warned = {\n\tinsertLeft: false,\n\tinsertRight: false,\n\tstoreName: false,\n};\n\nexport default class MagicString {\n\tconstructor(string, options = {}) {\n\t\tconst chunk = new Chunk(0, string.length, string);\n\n\t\tObject.defineProperties(this, {\n\t\t\toriginal: { writable: true, value: string },\n\t\t\toutro: { writable: true, value: '' },\n\t\t\tintro: { writable: true, value: '' },\n\t\t\tfirstChunk: { writable: true, value: chunk },\n\t\t\tlastChunk: { writable: true, value: chunk },\n\t\t\tlastSearchedChunk: { writable: true, value: chunk },\n\t\t\tbyStart: { writable: true, value: {} },\n\t\t\tbyEnd: { writable: true, value: {} },\n\t\t\tfilename: { writable: true, value: options.filename },\n\t\t\tindentExclusionRanges: { writable: true, value: options.indentExclusionRanges },\n\t\t\tsourcemapLocations: { writable: true, value: new BitSet() },\n\t\t\tstoredNames: { writable: true, value: {} },\n\t\t\tindentStr: { writable: true, value: guessIndent(string) },\n\t\t});\n\n\t\tif (DEBUG) {\n\t\t\tObject.defineProperty(this, 'stats', { value: new Stats() });\n\t\t}\n\n\t\tthis.byStart[0] = chunk;\n\t\tthis.byEnd[string.length] = chunk;\n\t}\n\n\taddSourcemapLocation(char) {\n\t\tthis.sourcemapLocations.add(char);\n\t}\n\n\tappend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.outro += content;\n\t\treturn this;\n\t}\n\n\tappendLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendLeft');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendLeft(content);\n\t\t} else {\n\t\t\tthis.intro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendLeft');\n\t\treturn this;\n\t}\n\n\tappendRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendRight(content);\n\t\t} else {\n\t\t\tthis.outro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendRight');\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst cloned = new MagicString(this.original, { filename: this.filename });\n\n\t\tlet originalChunk = this.firstChunk;\n\t\tlet clonedChunk = (cloned.firstChunk = cloned.lastSearchedChunk = originalChunk.clone());\n\n\t\twhile (originalChunk) {\n\t\t\tcloned.byStart[clonedChunk.start] = clonedChunk;\n\t\t\tcloned.byEnd[clonedChunk.end] = clonedChunk;\n\n\t\t\tconst nextOriginalChunk = originalChunk.next;\n\t\t\tconst nextClonedChunk = nextOriginalChunk && nextOriginalChunk.clone();\n\n\t\t\tif (nextClonedChunk) {\n\t\t\t\tclonedChunk.next = nextClonedChunk;\n\t\t\t\tnextClonedChunk.previous = clonedChunk;\n\n\t\t\t\tclonedChunk = nextClonedChunk;\n\t\t\t}\n\n\t\t\toriginalChunk = nextOriginalChunk;\n\t\t}\n\n\t\tcloned.lastChunk = clonedChunk;\n\n\t\tif (this.indentExclusionRanges) {\n\t\t\tcloned.indentExclusionRanges = this.indentExclusionRanges.slice();\n\t\t}\n\n\t\tcloned.sourcemapLocations = new BitSet(this.sourcemapLocations);\n\n\t\tcloned.intro = this.intro;\n\t\tcloned.outro = this.outro;\n\n\t\treturn cloned;\n\t}\n\n\tgenerateDecodedMap(options) {\n\t\toptions = options || {};\n\n\t\tconst sourceIndex = 0;\n\t\tconst names = Object.keys(this.storedNames);\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tconst locate = getLocator(this.original);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.firstChunk.eachNext((chunk) => {\n\t\t\tconst loc = locate(chunk.start);\n\n\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tmappings.addEdit(\n\t\t\t\t\tsourceIndex,\n\t\t\t\t\tchunk.content,\n\t\t\t\t\tloc,\n\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tmappings.addUneditedChunk(sourceIndex, chunk, this.original, loc, this.sourcemapLocations);\n\t\t\t}\n\n\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: [options.source ? getRelativePath(options.file || '', options.source) : null],\n\t\t\tsourcesContent: options.includeContent ? [this.original] : [null],\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\treturn this.indentStr === null ? '\\t' : this.indentStr;\n\t}\n\n\tindent(indentStr, options) {\n\t\tconst pattern = /^[^\\r\\n]/gm;\n\n\t\tif (isObject(indentStr)) {\n\t\t\toptions = indentStr;\n\t\t\tindentStr = undefined;\n\t\t}\n\n\t\tindentStr = indentStr !== undefined ? indentStr : this.indentStr || '\\t';\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\toptions = options || {};\n\n\t\t// Process exclusion ranges\n\t\tconst isExcluded = {};\n\n\t\tif (options.exclude) {\n\t\t\tconst exclusions =\n\t\t\t\ttypeof options.exclude[0] === 'number' ? [options.exclude] : options.exclude;\n\t\t\texclusions.forEach((exclusion) => {\n\t\t\t\tfor (let i = exclusion[0]; i < exclusion[1]; i += 1) {\n\t\t\t\t\tisExcluded[i] = true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet shouldIndentNextCharacter = options.indentStart !== false;\n\t\tconst replacer = (match) => {\n\t\t\tif (shouldIndentNextCharacter) return `${indentStr}${match}`;\n\t\t\tshouldIndentNextCharacter = true;\n\t\t\treturn match;\n\t\t};\n\n\t\tthis.intro = this.intro.replace(pattern, replacer);\n\n\t\tlet charIndex = 0;\n\t\tlet chunk = this.firstChunk;\n\n\t\twhile (chunk) {\n\t\t\tconst end = chunk.end;\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\tchunk.content = chunk.content.replace(pattern, replacer);\n\n\t\t\t\t\tif (chunk.content.length) {\n\t\t\t\t\t\tshouldIndentNextCharacter = chunk.content[chunk.content.length - 1] === '\\n';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcharIndex = chunk.start;\n\n\t\t\t\twhile (charIndex < end) {\n\t\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\t\tconst char = this.original[charIndex];\n\n\t\t\t\t\t\tif (char === '\\n') {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = true;\n\t\t\t\t\t\t} else if (char !== '\\r' && shouldIndentNextCharacter) {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = false;\n\n\t\t\t\t\t\t\tif (charIndex === chunk.start) {\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis._splitChunk(chunk, charIndex);\n\t\t\t\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tcharIndex += 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcharIndex = chunk.end;\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tthis.outro = this.outro.replace(pattern, replacer);\n\n\t\treturn this;\n\t}\n\n\tinsert() {\n\t\tthrow new Error(\n\t\t\t'magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)'\n\t\t);\n\t}\n\n\tinsertLeft(index, content) {\n\t\tif (!warned.insertLeft) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead'\n\t\t\t); // eslint-disable-line no-console\n\t\t\twarned.insertLeft = true;\n\t\t}\n\n\t\treturn this.appendLeft(index, content);\n\t}\n\n\tinsertRight(index, content) {\n\t\tif (!warned.insertRight) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead'\n\t\t\t); // eslint-disable-line no-console\n\t\t\twarned.insertRight = true;\n\t\t}\n\n\t\treturn this.prependRight(index, content);\n\t}\n\n\tmove(start, end, index) {\n\t\tif (index >= start && index <= end) throw new Error('Cannot move a selection inside itself');\n\n\t\tif (DEBUG) this.stats.time('move');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\t\tthis._split(index);\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tconst oldLeft = first.previous;\n\t\tconst oldRight = last.next;\n\n\t\tconst newRight = this.byStart[index];\n\t\tif (!newRight && last === this.lastChunk) return this;\n\t\tconst newLeft = newRight ? newRight.previous : this.lastChunk;\n\n\t\tif (oldLeft) oldLeft.next = oldRight;\n\t\tif (oldRight) oldRight.previous = oldLeft;\n\n\t\tif (newLeft) newLeft.next = first;\n\t\tif (newRight) newRight.previous = last;\n\n\t\tif (!first.previous) this.firstChunk = last.next;\n\t\tif (!last.next) {\n\t\t\tthis.lastChunk = first.previous;\n\t\t\tthis.lastChunk.next = null;\n\t\t}\n\n\t\tfirst.previous = newLeft;\n\t\tlast.next = newRight || null;\n\n\t\tif (!newLeft) this.firstChunk = first;\n\t\tif (!newRight) this.lastChunk = last;\n\n\t\tif (DEBUG) this.stats.timeEnd('move');\n\t\treturn this;\n\t}\n\n\toverwrite(start, end, content, options) {\n\t\tif (typeof content !== 'string') throw new TypeError('replacement content must be a string');\n\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (end > this.original.length) throw new Error('end is out of bounds');\n\t\tif (start === end)\n\t\t\tthrow new Error(\n\t\t\t\t'Cannot overwrite a zero-length range – use appendLeft or prependRight instead'\n\t\t\t);\n\n\t\tif (DEBUG) this.stats.time('overwrite');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tif (options === true) {\n\t\t\tif (!warned.storeName) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string'\n\t\t\t\t); // eslint-disable-line no-console\n\t\t\t\twarned.storeName = true;\n\t\t\t}\n\n\t\t\toptions = { storeName: true };\n\t\t}\n\t\tconst storeName = options !== undefined ? options.storeName : false;\n\t\tconst contentOnly = options !== undefined ? options.contentOnly : false;\n\n\t\tif (storeName) {\n\t\t\tconst original = this.original.slice(start, end);\n\t\t\tObject.defineProperty(this.storedNames, original, { writable: true, value: true, enumerable: true });\n\t\t}\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tif (first) {\n\t\t\tlet chunk = first;\n\t\t\twhile (chunk !== last) {\n\t\t\t\tif (chunk.next !== this.byStart[chunk.end]) {\n\t\t\t\t\tthrow new Error('Cannot overwrite across a split point');\n\t\t\t\t}\n\t\t\t\tchunk = chunk.next;\n\t\t\t\tchunk.edit('', false);\n\t\t\t}\n\n\t\t\tfirst.edit(content, storeName, contentOnly);\n\t\t} else {\n\t\t\t// must be inserting at the end\n\t\t\tconst newChunk = new Chunk(start, end, '').edit(content, storeName);\n\n\t\t\t// TODO last chunk in the array may not be the last chunk, if it's moved...\n\t\t\tlast.next = newChunk;\n\t\t\tnewChunk.previous = last;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('overwrite');\n\t\treturn this;\n\t}\n\n\tprepend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.intro = content + this.intro;\n\t\treturn this;\n\t}\n\n\tprependLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependLeft(content);\n\t\t} else {\n\t\t\tthis.intro = content + this.intro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tprependRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependRight(content);\n\t\t} else {\n\t\t\tthis.outro = content + this.outro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tremove(start, end) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('remove');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.intro = '';\n\t\t\tchunk.outro = '';\n\t\t\tchunk.edit('');\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('remove');\n\t\treturn this;\n\t}\n\n\tlastChar() {\n\t\tif (this.outro.length) return this.outro[this.outro.length - 1];\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length) return chunk.outro[chunk.outro.length - 1];\n\t\t\tif (chunk.content.length) return chunk.content[chunk.content.length - 1];\n\t\t\tif (chunk.intro.length) return chunk.intro[chunk.intro.length - 1];\n\t\t} while ((chunk = chunk.previous));\n\t\tif (this.intro.length) return this.intro[this.intro.length - 1];\n\t\treturn '';\n\t}\n\n\tlastLine() {\n\t\tlet lineIndex = this.outro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.outro.substr(lineIndex + 1);\n\t\tlet lineStr = this.outro;\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length > 0) {\n\t\t\t\tlineIndex = chunk.outro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.outro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.outro + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.content.length > 0) {\n\t\t\t\tlineIndex = chunk.content.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.content.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.content + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.intro.length > 0) {\n\t\t\t\tlineIndex = chunk.intro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.intro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.intro + lineStr;\n\t\t\t}\n\t\t} while ((chunk = chunk.previous));\n\t\tlineIndex = this.intro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.intro.substr(lineIndex + 1) + lineStr;\n\t\treturn this.intro + lineStr;\n\t}\n\n\tslice(start = 0, end = this.original.length) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tlet result = '';\n\n\t\t// find start chunk\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk && (chunk.start > start || chunk.end <= start)) {\n\t\t\t// found end chunk before start\n\t\t\tif (chunk.start < end && chunk.end >= end) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tif (chunk && chunk.edited && chunk.start !== start)\n\t\t\tthrow new Error(`Cannot use replaced character ${start} as slice start anchor.`);\n\n\t\tconst startChunk = chunk;\n\t\twhile (chunk) {\n\t\t\tif (chunk.intro && (startChunk !== chunk || chunk.start === start)) {\n\t\t\t\tresult += chunk.intro;\n\t\t\t}\n\n\t\t\tconst containsEnd = chunk.start < end && chunk.end >= end;\n\t\t\tif (containsEnd && chunk.edited && chunk.end !== end)\n\t\t\t\tthrow new Error(`Cannot use replaced character ${end} as slice end anchor.`);\n\n\t\t\tconst sliceStart = startChunk === chunk ? start - chunk.start : 0;\n\t\t\tconst sliceEnd = containsEnd ? chunk.content.length + end - chunk.end : chunk.content.length;\n\n\t\t\tresult += chunk.content.slice(sliceStart, sliceEnd);\n\n\t\t\tif (chunk.outro && (!containsEnd || chunk.end === end)) {\n\t\t\t\tresult += chunk.outro;\n\t\t\t}\n\n\t\t\tif (containsEnd) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t// TODO deprecate this? not really very useful\n\tsnip(start, end) {\n\t\tconst clone = this.clone();\n\t\tclone.remove(0, start);\n\t\tclone.remove(end, clone.original.length);\n\n\t\treturn clone;\n\t}\n\n\t_split(index) {\n\t\tif (this.byStart[index] || this.byEnd[index]) return;\n\n\t\tif (DEBUG) this.stats.time('_split');\n\n\t\tlet chunk = this.lastSearchedChunk;\n\t\tconst searchForward = index > chunk.end;\n\n\t\twhile (chunk) {\n\t\t\tif (chunk.contains(index)) return this._splitChunk(chunk, index);\n\n\t\t\tchunk = searchForward ? this.byStart[chunk.end] : this.byEnd[chunk.start];\n\t\t}\n\t}\n\n\t_splitChunk(chunk, index) {\n\t\tif (chunk.edited && chunk.content.length) {\n\t\t\t// zero-length edited chunks are a special case (overlapping replacements)\n\t\t\tconst loc = getLocator(this.original)(index);\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot split a chunk that has already been edited (${loc.line}:${loc.column} – \"${chunk.original}\")`\n\t\t\t);\n\t\t}\n\n\t\tconst newChunk = chunk.split(index);\n\n\t\tthis.byEnd[index] = chunk;\n\t\tthis.byStart[index] = newChunk;\n\t\tthis.byEnd[newChunk.end] = newChunk;\n\n\t\tif (chunk === this.lastChunk) this.lastChunk = newChunk;\n\n\t\tthis.lastSearchedChunk = chunk;\n\t\tif (DEBUG) this.stats.timeEnd('_split');\n\t\treturn true;\n\t}\n\n\ttoString() {\n\t\tlet str = this.intro;\n\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk) {\n\t\t\tstr += chunk.toString();\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn str + this.outro;\n\t}\n\n\tisEmpty() {\n\t\tlet chunk = this.firstChunk;\n\t\tdo {\n\t\t\tif (\n\t\t\t\t(chunk.intro.length && chunk.intro.trim()) ||\n\t\t\t\t(chunk.content.length && chunk.content.trim()) ||\n\t\t\t\t(chunk.outro.length && chunk.outro.trim())\n\t\t\t)\n\t\t\t\treturn false;\n\t\t} while ((chunk = chunk.next));\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\tlet chunk = this.firstChunk;\n\t\tlet length = 0;\n\t\tdo {\n\t\t\tlength += chunk.intro.length + chunk.content.length + chunk.outro.length;\n\t\t} while ((chunk = chunk.next));\n\t\treturn length;\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimEndAborted(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tlet chunk = this.lastChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimEnd(rx);\n\n\t\t\t// if chunk was trimmed, we have a new lastChunk\n\t\t\tif (chunk.end !== end) {\n\t\t\t\tif (this.lastChunk === chunk) {\n\t\t\t\t\tthis.lastChunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.previous;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimEnd(charType) {\n\t\tthis.trimEndAborted(charType);\n\t\treturn this;\n\t}\n\ttrimStartAborted(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tlet chunk = this.firstChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimStart(rx);\n\n\t\t\tif (chunk.end !== end) {\n\t\t\t\t// special case...\n\t\t\t\tif (chunk === this.lastChunk) this.lastChunk = chunk.next;\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.next;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimStart(charType) {\n\t\tthis.trimStartAborted(charType);\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport SourceMap from './SourceMap.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\n\nconst hasOwnProp = Object.prototype.hasOwnProperty;\n\nexport default class Bundle {\n\tconstructor(options = {}) {\n\t\tthis.intro = options.intro || '';\n\t\tthis.separator = options.separator !== undefined ? options.separator : '\\n';\n\t\tthis.sources = [];\n\t\tthis.uniqueSources = [];\n\t\tthis.uniqueSourceIndexByFilename = {};\n\t}\n\n\taddSource(source) {\n\t\tif (source instanceof MagicString) {\n\t\t\treturn this.addSource({\n\t\t\t\tcontent: source,\n\t\t\t\tfilename: source.filename,\n\t\t\t\tseparator: this.separator,\n\t\t\t});\n\t\t}\n\n\t\tif (!isObject(source) || !source.content) {\n\t\t\tthrow new Error(\n\t\t\t\t'bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`'\n\t\t\t);\n\t\t}\n\n\t\t['filename', 'indentExclusionRanges', 'separator'].forEach((option) => {\n\t\t\tif (!hasOwnProp.call(source, option)) source[option] = source.content[option];\n\t\t});\n\n\t\tif (source.separator === undefined) {\n\t\t\t// TODO there's a bunch of this sort of thing, needs cleaning up\n\t\t\tsource.separator = this.separator;\n\t\t}\n\n\t\tif (source.filename) {\n\t\t\tif (!hasOwnProp.call(this.uniqueSourceIndexByFilename, source.filename)) {\n\t\t\t\tthis.uniqueSourceIndexByFilename[source.filename] = this.uniqueSources.length;\n\t\t\t\tthis.uniqueSources.push({ filename: source.filename, content: source.content.original });\n\t\t\t} else {\n\t\t\t\tconst uniqueSource = this.uniqueSources[this.uniqueSourceIndexByFilename[source.filename]];\n\t\t\t\tif (source.content.original !== uniqueSource.content) {\n\t\t\t\t\tthrow new Error(`Illegal source: same filename (${source.filename}), different contents`);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.sources.push(source);\n\t\treturn this;\n\t}\n\n\tappend(str, options) {\n\t\tthis.addSource({\n\t\t\tcontent: new MagicString(str),\n\t\t\tseparator: (options && options.separator) || '',\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst bundle = new Bundle({\n\t\t\tintro: this.intro,\n\t\t\tseparator: this.separator,\n\t\t});\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tbundle.addSource({\n\t\t\t\tfilename: source.filename,\n\t\t\t\tcontent: source.content.clone(),\n\t\t\t\tseparator: source.separator,\n\t\t\t});\n\t\t});\n\n\t\treturn bundle;\n\t}\n\n\tgenerateDecodedMap(options = {}) {\n\t\tconst names = [];\n\t\tthis.sources.forEach((source) => {\n\t\t\tObject.keys(source.content.storedNames).forEach((name) => {\n\t\t\t\tif (!~names.indexOf(name)) names.push(name);\n\t\t\t});\n\t\t});\n\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tif (i > 0) {\n\t\t\t\tmappings.advance(this.separator);\n\t\t\t}\n\n\t\t\tconst sourceIndex = source.filename ? this.uniqueSourceIndexByFilename[source.filename] : -1;\n\t\t\tconst magicString = source.content;\n\t\t\tconst locate = getLocator(magicString.original);\n\n\t\t\tif (magicString.intro) {\n\t\t\t\tmappings.advance(magicString.intro);\n\t\t\t}\n\n\t\t\tmagicString.firstChunk.eachNext((chunk) => {\n\t\t\t\tconst loc = locate(chunk.start);\n\n\t\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\t\tif (source.filename) {\n\t\t\t\t\tif (chunk.edited) {\n\t\t\t\t\t\tmappings.addEdit(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk.content,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmappings.addUneditedChunk(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk,\n\t\t\t\t\t\t\tmagicString.original,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tmagicString.sourcemapLocations\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmappings.advance(chunk.content);\n\t\t\t\t}\n\n\t\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t\t});\n\n\t\t\tif (magicString.outro) {\n\t\t\t\tmappings.advance(magicString.outro);\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.file ? getRelativePath(options.file, source.filename) : source.filename;\n\t\t\t}),\n\t\t\tsourcesContent: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.includeContent ? source.content : null;\n\t\t\t}),\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\tconst indentStringCounts = {};\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tconst indentStr = source.content.indentStr;\n\n\t\t\tif (indentStr === null) return;\n\n\t\t\tif (!indentStringCounts[indentStr]) indentStringCounts[indentStr] = 0;\n\t\t\tindentStringCounts[indentStr] += 1;\n\t\t});\n\n\t\treturn (\n\t\t\tObject.keys(indentStringCounts).sort((a, b) => {\n\t\t\t\treturn indentStringCounts[a] - indentStringCounts[b];\n\t\t\t})[0] || '\\t'\n\t\t);\n\t}\n\n\tindent(indentStr) {\n\t\tif (!arguments.length) {\n\t\t\tindentStr = this.getIndentString();\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\tlet trailingNewline = !this.intro || this.intro.slice(-1) === '\\n';\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\tconst indentStart = trailingNewline || (i > 0 && /\\r?\\n$/.test(separator));\n\n\t\t\tsource.content.indent(indentStr, {\n\t\t\t\texclude: source.indentExclusionRanges,\n\t\t\t\tindentStart, //: trailingNewline || /\\r?\\n$/.test( separator )  //true///\\r?\\n/.test( separator )\n\t\t\t});\n\n\t\t\ttrailingNewline = source.content.lastChar() === '\\n';\n\t\t});\n\n\t\tif (this.intro) {\n\t\t\tthis.intro =\n\t\t\t\tindentStr +\n\t\t\t\tthis.intro.replace(/^[^\\n]/gm, (match, index) => {\n\t\t\t\t\treturn index > 0 ? indentStr + match : match;\n\t\t\t\t});\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tprepend(str) {\n\t\tthis.intro = str + this.intro;\n\t\treturn this;\n\t}\n\n\ttoString() {\n\t\tconst body = this.sources\n\t\t\t.map((source, i) => {\n\t\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\t\tconst str = (i > 0 ? separator : '') + source.content.toString();\n\n\t\t\t\treturn str;\n\t\t\t})\n\t\t\t.join('');\n\n\t\treturn this.intro + body;\n\t}\n\n\tisEmpty() {\n\t\tif (this.intro.length && this.intro.trim()) return false;\n\t\tif (this.sources.some((source) => !source.content.isEmpty())) return false;\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\treturn this.sources.reduce(\n\t\t\t(length, source) => length + source.content.length(),\n\t\t\tthis.intro.length\n\t\t);\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimStart(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\t\tthis.intro = this.intro.replace(rx, '');\n\n\t\tif (!this.intro) {\n\t\t\tlet source;\n\t\t\tlet i = 0;\n\n\t\t\tdo {\n\t\t\t\tsource = this.sources[i++];\n\t\t\t\tif (!source) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} while (!source.content.trimStartAborted(charType));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\ttrimEnd(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tlet source;\n\t\tlet i = this.sources.length - 1;\n\n\t\tdo {\n\t\t\tsource = this.sources[i--];\n\t\t\tif (!source) {\n\t\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (!source.content.trimEndAborted(charType));\n\n\t\treturn this;\n\t}\n}\n"], "names": ["const", "let", "this"], "mappings": ";;AAAe,IAAM,MAAM,GAC1B,eAAW,CAAC,GAAG,EAAE;AAClB,CAAE,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;AAC3D,EAAC;AACF;iBACC,oBAAI,CAAC,EAAE;AACR,CAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,EAAC;AACF;iBACC,oBAAI,CAAC,EAAE;AACR,CAAE,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAChD;;ACXc,IAAM,KAAK,GACzB,cAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;AAClC,CAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,CAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,CAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC1B;AACA,CAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAClB,CAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAClB;AACA,CAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACzB,CAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACzB,CAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB;AACA;AACA,CAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;AAC5C,EAAG,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;AACxC,EAAG,CAAC,CAAC;AACJ,EAAC;AACF;gBACC,kCAAW,OAAO,EAAE;AACrB,CAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;AACvB,EAAC;AACF;gBACC,oCAAY,OAAO,EAAE;AACtB,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;AACnC,EAAC;AACF;gBACC,0BAAQ;AACT,CAAEA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/D;AACA,CAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,CAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,CAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,CAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC,CAAE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;AACA,CAAE,OAAO,KAAK,CAAC;AACd,EAAC;AACF;gBACC,8BAAS,KAAK,EAAE;AACjB,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AAC/C,EAAC;AACF;gBACC,8BAAS,EAAE,EAAE;AACd,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB,CAAE,OAAO,KAAK,EAAE;AAChB,EAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACb,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AACtB,EAAG;AACF,EAAC;AACF;gBACC,sCAAa,EAAE,EAAE;AAClB,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB,CAAE,OAAO,KAAK,EAAE;AAChB,EAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACb,EAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;AAC1B,EAAG;AACF,EAAC;AACF;gBACC,sBAAK,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;AACvC,CAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACzB,CAAE,IAAI,CAAC,WAAW,EAAE;AACpB,EAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACnB,EAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACnB,EAAG;AACH,CAAE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC7B;AACA,CAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACrB;AACA,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;gBACC,oCAAY,OAAO,EAAE;AACtB,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACnC,EAAC;AACF;gBACC,sCAAa,OAAO,EAAE;AACvB,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACnC,EAAC;AACF;gBACC,wBAAM,KAAK,EAAE;AACd,CAAED,IAAM,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACxC;AACA,CAAEA,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC5D,CAAEA,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACxD;AACA,CAAE,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;AACjC;AACA,CAAEA,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;AAC7D,CAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9B,CAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAClB;AACA,CAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;AACnB;AACA,CAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AACnB;AACA,EAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC5B,EAAG,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACrB,EAAG,MAAM;AACT,EAAG,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;AACjC,EAAG;AACH;AACA,CAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC5B,CAAE,IAAI,QAAQ,CAAC,IAAI,IAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAC;AACvD,CAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B,CAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACvB;AACA,CAAE,OAAO,QAAQ,CAAC;AACjB,EAAC;AACF;gBACC,gCAAW;AACZ,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/C,EAAC;AACF;gBACC,4BAAQ,EAAE,EAAE;AACb,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;AACA,CAAEA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/C;AACA,CAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,EAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;AACjC,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AACtE,GAAI;AACJ,EAAG,OAAO,IAAI,CAAC;AACf,EAAG,MAAM;AACT,EAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAClC;AACA,EAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3C,EAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACtC,EAAG;AACF,EAAC;AACF;gBACC,gCAAU,EAAE,EAAE;AACf,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;AACA,CAAEA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/C;AACA,CAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,EAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;AACjC,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1C,GAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AACnC,GAAI;AACJ,EAAG,OAAO,IAAI,CAAC;AACf,EAAG,MAAM;AACT,EAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAClC;AACA,EAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3C,EAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACtC,EAAG;AACF;;ACtJDC,IAAI,IAAI,eAAS;AACjB,CAAC,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;AAC5F,CAAC,CAAC;AACF,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;AACxE,CAAC,IAAI,aAAI,GAAG,WAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAC,CAAC;AAChE,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AACzC,CAAC,IAAI,aAAI,GAAG,WAAK,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAC,CAAC;AAC9D,CAAC;AACD;IACqB,SAAS,GAC7B,kBAAW,CAAC,UAAU,EAAE;AACzB,CAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACnB,CAAE,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;AAC9B,CAAE,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACpC,CAAE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;AAClD,CAAE,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;AAChC,CAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC7C,EAAC;AACF;oBACC,gCAAW;AACZ,CAAE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAC;AACF;oBACC,0BAAQ;AACT,CAAE,OAAO,6CAA6C,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9E;;AC3Bc,SAAS,WAAW,CAAC,IAAI,EAAE;AAC1C,CAACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,CAACA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAE,IAAI,WAAK,MAAM,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;AAC1D,CAACA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAE,IAAI,WAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;AAC5D;AACA,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AACjD,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA;AACA;AACA;AACA,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;AACrC,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA;AACA,CAACA,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,WAAE,QAAQ,EAAE,OAAO,EAAK;AAClD,EAAEA,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACvC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACd;AACA,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrC;;ACxBe,SAAS,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;AAClD,CAACA,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvC,CAACA,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACnC;AACA,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AACjB;AACA,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;AACrC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;AACpB,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;AAClB,EAAE;AACF;AACA,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE;AACvB,EAAEC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AAC3B,EAAE,OAAO,CAAC,EAAE,IAAE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAC;AAClC,EAAE;AACF;AACA,CAAC,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5C;;ACjBAD,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC3C;AACe,SAAS,QAAQ,CAAC,KAAK,EAAE;AACxC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC;AACnD;;ACJe,SAAS,UAAU,CAAC,MAAM,EAAE;AAC3C,CAACA,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1C,CAACA,IAAM,WAAW,GAAG,EAAE,CAAC;AACxB;AACA,CAAC,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzD,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrC,EAAE;AACF;AACA,CAAC,OAAO,SAAS,MAAM,CAAC,KAAK,EAAE;AAC/B,EAAEA,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAEA,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;AAC7B,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;AAChB,GAAGD,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,GAAG,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;AAC/B,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAI,MAAM;AACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACd,IAAI;AACJ,GAAG;AACH,EAAEA,IAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,EAAEA,IAAM,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,EAAE,OAAO,QAAE,IAAI,UAAE,MAAM,EAAE,CAAC;AAC1B,EAAE,CAAC;AACH;;ACxBe,IAAM,QAAQ,GAC5B,iBAAW,CAAC,KAAK,EAAE;AACpB,CAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,CAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC7B,CAAE,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AAC/B,CAAE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAChB,CAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC3D,CAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACrB,EAAC;AACF;mBACC,4BAAQ,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;AAC/C,CAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,EAAGA,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AACjF,EAAG,IAAI,SAAS,IAAI,CAAC,EAAE;AACvB,GAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5B,GAAI;AACJ,EAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClC,EAAG,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AAC3B,EAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvC,EAAG;AACH;AACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACxB,CAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACrB,EAAC;AACF;mBACC,8CAAiB,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE;AACzE,CAAEC,IAAI,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC;AACtC,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB;AACA,CAAE,OAAO,iBAAiB,GAAG,KAAK,CAAC,GAAG,EAAE;AACxC,EAAG,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;AACzE,GAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AACzF,GAAI;AACJ;AACA,EAAG,IAAI,QAAQ,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;AAC7C,GAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;AAClB,GAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AACnB,GAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;AAChC,GAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC7D,GAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AACjC,GAAI,KAAK,GAAG,IAAI,CAAC;AACjB,GAAI,MAAM;AACV,GAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;AACpB,GAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;AAClC,GAAI,KAAK,GAAG,KAAK,CAAC;AAClB,GAAI;AACJ;AACA,EAAG,iBAAiB,IAAI,CAAC,CAAC;AAC1B,EAAG;AACH;AACA,CAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACrB,EAAC;AACF;mBACC,4BAAQ,GAAG,EAAE;AACd,CAAE,IAAI,CAAC,GAAG,IAAE,SAAO;AACnB;AACA,CAAED,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,CAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,EAAG,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,GAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,GAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC7D,GAAI;AACJ,EAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AAChC,EAAG;AACH;AACA,CAAE,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;AAC5D;;ACzDDD,IAAM,CAAC,GAAG,IAAI,CAAC;AACf;AACAA,IAAM,MAAM,GAAG;AACf,CAAC,UAAU,EAAE,KAAK;AAClB,CAAC,WAAW,EAAE,KAAK;AACnB,CAAC,SAAS,EAAE,KAAK;AACjB,CAAC,CAAC;AACF;IACqB,WAAW,GAC/B,oBAAW,CAAC,MAAM,EAAE,OAAY,EAAE;kCAAP,GAAG;AAAK;AACpC,CAAEA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACpD;AACA,CAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;AAC9C,EAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACvC,EAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACvC,EAAG,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAC/C,EAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAC9C,EAAG,iBAAiB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AACtD,EAAG,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACzC,EAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AACvC,EAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE;AACxD,EAAG,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,qBAAqB,EAAE;AAClF,EAAG,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;AAC9D,EAAG,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;AAC7C,EAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE;AAC5D,EAAG,CAAC,CAAC;AAKL;AACA,CAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC1B,CAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AACnC,EAAC;AACF;sBACC,sDAAqB,IAAI,EAAE;AAC5B,CAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,EAAC;AACF;sBACC,0BAAO,OAAO,EAAE;AACjB,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;AACzF;AACA,CAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;AACxB,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,kCAAW,KAAK,EAAE,OAAO,EAAE;AAC5B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;AACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;AACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;AACA,CAAE,IAAI,KAAK,EAAE;AACb,EAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AAC7B,EAAG,MAAM;AACT,EAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;AACzB,EAAG;AAGH,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,oCAAY,KAAK,EAAE,OAAO,EAAE;AAC7B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;AACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;AACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC;AACA,CAAE,IAAI,KAAK,EAAE;AACb,EAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9B,EAAG,MAAM;AACT,EAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;AACzB,EAAG;AAGH,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,0BAAQ;AACT,CAAEA,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC7E;AACA,CAAEC,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC;AACtC,CAAEA,IAAI,WAAW,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;AAC3F;AACA,CAAE,OAAO,aAAa,EAAE;AACxB,EAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;AACnD,EAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;AAC/C;AACA,EAAGD,IAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAC;AAChD,EAAGA,IAAM,eAAe,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAC1E;AACA,EAAG,IAAI,eAAe,EAAE;AACxB,GAAI,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC;AACvC,GAAI,eAAe,CAAC,QAAQ,GAAG,WAAW,CAAC;AAC3C;AACA,GAAI,WAAW,GAAG,eAAe,CAAC;AAClC,GAAI;AACJ;AACA,EAAG,aAAa,GAAG,iBAAiB,CAAC;AACrC,EAAG;AACH;AACA,CAAE,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC;AACjC;AACA,CAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAClC,EAAG,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;AACrE,EAAG;AACH;AACA,CAAE,MAAM,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAClE;AACA,CAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B,CAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B;AACA,CAAE,OAAO,MAAM,CAAC;AACf,EAAC;AACF;sBACC,kDAAmB,OAAO,EAAE;;AAAC;AAC9B,CAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC1B;AACA,CAAEA,IAAM,WAAW,GAAG,CAAC,CAAC;AACxB,CAAEA,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9C,CAAEA,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;AACA,CAAEA,IAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C;AACA,CAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,EAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,EAAG;AACH;AACA,CAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,WAAE,KAAK,EAAK;AACtC,EAAGA,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnC;AACA,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;AACzD;AACA,EAAG,IAAI,KAAK,CAAC,MAAM,EAAE;AACrB,GAAI,QAAQ,CAAC,OAAO;AACpB,IAAK,WAAW;AAChB,IAAK,KAAK,CAAC,OAAO;AAClB,IAAK,GAAG;AACR,IAAK,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzD,IAAK,CAAC;AACN,GAAI,MAAM;AACV,GAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAEE,QAAI,CAAC,QAAQ,EAAE,GAAG,EAAEA,QAAI,CAAC,kBAAkB,CAAC,CAAC;AAC/F,GAAI;AACJ;AACA,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;AACzD,EAAG,CAAC,CAAC;AACL;AACA,CAAE,OAAO;AACT,EAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;AAChE,EAAG,OAAO,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACzF,EAAG,cAAc,EAAE,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;AACpE,SAAG,KAAK;AACR,EAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;AACzB,EAAG,CAAC;AACH,EAAC;AACF;sBACC,oCAAY,OAAO,EAAE;AACtB,CAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;AACxD,EAAC;AACF;sBACC,8CAAkB;AACnB,CAAE,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;AACxD,EAAC;AACF;sBACC,0BAAO,SAAS,EAAE,OAAO,EAAE;AAC5B,CAAEF,IAAM,OAAO,GAAG,YAAY,CAAC;AAC/B;AACA,CAAE,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC3B,EAAG,OAAO,GAAG,SAAS,CAAC;AACvB,EAAG,SAAS,GAAG,SAAS,CAAC;AACzB,EAAG;AACH;AACA,CAAE,SAAS,GAAG,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;AAC3E;AACA,CAAE,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;AACpC;AACA,CAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC1B;AACA;AACA,CAAEA,IAAM,UAAU,GAAG,EAAE,CAAC;AACxB;AACA,CAAE,IAAI,OAAO,CAAC,OAAO,EAAE;AACvB,EAAGA,IAAM,UAAU;AACnB,GAAI,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;AACjF,EAAG,UAAU,CAAC,OAAO,WAAE,SAAS,EAAK;AACrC,GAAI,KAAKC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACzD,IAAK,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC1B,IAAK;AACL,GAAI,CAAC,CAAC;AACN,EAAG;AACH;AACA,CAAEA,IAAI,yBAAyB,GAAG,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;AAChE,CAAED,IAAM,QAAQ,aAAI,KAAK,EAAK;AAC9B,EAAG,IAAI,yBAAyB,IAAE,aAAU,YAAY,SAAQ;AAChE,EAAG,yBAAyB,GAAG,IAAI,CAAC;AACpC,EAAG,OAAO,KAAK,CAAC;AAChB,EAAG,CAAC;AACJ;AACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACrD;AACA,CAAEC,IAAI,SAAS,GAAG,CAAC,CAAC;AACpB,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B;AACA,CAAE,OAAO,KAAK,EAAE;AAChB,EAAGD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACzB;AACA,EAAG,IAAI,KAAK,CAAC,MAAM,EAAE;AACrB,GAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AAChC,IAAK,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC9D;AACA,IAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;AAC/B,KAAM,yBAAyB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC;AACnF,KAAM;AACN,IAAK;AACL,GAAI,MAAM;AACV,GAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;AAC5B;AACA,GAAI,OAAO,SAAS,GAAG,GAAG,EAAE;AAC5B,IAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AACjC,KAAMA,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC5C;AACA,KAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AACzB,MAAO,yBAAyB,GAAG,IAAI,CAAC;AACxC,MAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,yBAAyB,EAAE;AAC7D,MAAO,yBAAyB,GAAG,KAAK,CAAC;AACzC;AACA,MAAO,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE;AACtC,OAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACtC,OAAQ,MAAM;AACd,OAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC3C,OAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AAC3B,OAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACtC,OAAQ;AACR,MAAO;AACP,KAAM;AACN;AACA,IAAK,SAAS,IAAI,CAAC,CAAC;AACpB,IAAK;AACL,GAAI;AACJ;AACA,EAAG,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC;AACzB,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AACtB,EAAG;AACH;AACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACrD;AACA,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,4BAAS;AACV,CAAE,MAAM,IAAI,KAAK;AACjB,EAAG,iFAAiF;AACpF,EAAG,CAAC;AACH,EAAC;AACF;sBACC,kCAAW,KAAK,EAAE,OAAO,EAAE;AAC5B,CAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AAC1B,EAAG,OAAO,CAAC,IAAI;AACf,GAAI,oFAAoF;AACxF,GAAI,CAAC;AACL,EAAG,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AAC5B,EAAG;AACH;AACA,CAAE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACxC,EAAC;AACF;sBACC,oCAAY,KAAK,EAAE,OAAO,EAAE;AAC7B,CAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;AAC3B,EAAG,OAAO,CAAC,IAAI;AACf,GAAI,uFAAuF;AAC3F,GAAI,CAAC;AACL,EAAG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;AAC7B,EAAG;AACH;AACA,CAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1C,EAAC;AACF;sBACC,sBAAK,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACzB,CAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,GAAC;AAG/F;AACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB,CAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnB,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;AACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/B;AACA,CAAEA,IAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;AACjC,CAAEA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AAC7B;AACA,CAAEA,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvC,CAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,IAAE,OAAO,IAAI,GAAC;AACxD,CAAEA,IAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AAChE;AACA,CAAE,IAAI,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,QAAQ,GAAC;AACvC,CAAE,IAAI,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,GAAC;AAC5C;AACA,CAAE,IAAI,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,KAAK,GAAC;AACpC,CAAE,IAAI,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAC;AACzC;AACA,CAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAC;AACnD,CAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AAClB,EAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;AACnC,EAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;AAC9B,EAAG;AACH;AACA,CAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC3B,CAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC;AAC/B;AACA,CAAE,IAAI,CAAC,OAAO,IAAE,IAAI,CAAC,UAAU,GAAG,KAAK,GAAC;AACxC,CAAE,IAAI,CAAC,QAAQ,IAAE,IAAI,CAAC,SAAS,GAAG,IAAI,GAAC;AAGvC,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,gCAAU,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;AACzC,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,GAAC;AAC/F;AACA,CAAE,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAClD,CAAE,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAC9C;AACA,CAAE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,GAAC;AAC1E,CAAE,IAAI,KAAK,KAAK,GAAG;AACnB,IAAG,MAAM,IAAI,KAAK;AAClB,GAAI,+EAA+E;AACnF,GAAI,GAAC;AAGL;AACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB,CAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnB;AACA,CAAE,IAAI,OAAO,KAAK,IAAI,EAAE;AACxB,EAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAC1B,GAAI,OAAO,CAAC,IAAI;AAChB,IAAK,+HAA+H;AACpI,IAAK,CAAC;AACN,GAAI,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAC5B,GAAI;AACJ;AACA,EAAG,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AACjC,EAAG;AACH,CAAEA,IAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;AACtE,CAAEA,IAAM,WAAW,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;AAC1E;AACA,CAAE,IAAI,SAAS,EAAE;AACjB,EAAGA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACpD,EAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;AACxG,EAAG;AACH;AACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/B;AACA,CAAE,IAAI,KAAK,EAAE;AACb,EAAGC,IAAI,KAAK,GAAG,KAAK,CAAC;AACrB,EAAG,OAAO,KAAK,KAAK,IAAI,EAAE;AAC1B,GAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAChD,IAAK,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC9D,IAAK;AACL,GAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AACvB,GAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC1B,GAAI;AACJ;AACA,EAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;AAC/C,EAAG,MAAM;AACT;AACA,EAAGD,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACvE;AACA;AACA,EAAG,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACxB,EAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC5B,EAAG;AAGH,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,4BAAQ,OAAO,EAAE;AAClB,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;AACzF;AACA,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACpC,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,oCAAY,KAAK,EAAE,OAAO,EAAE;AAC7B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;AACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;AACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;AACA,CAAE,IAAI,KAAK,EAAE;AACb,EAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9B,EAAG,MAAM;AACT,EAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACrC,EAAG;AAGH,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,sCAAa,KAAK,EAAE,OAAO,EAAE;AAC9B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;AACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;AACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC;AACA,CAAE,IAAI,KAAK,EAAE;AACb,EAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAG,MAAM;AACT,EAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACrC,EAAG;AAGH,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,0BAAO,KAAK,EAAE,GAAG,EAAE;AACpB,CAAE,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAClD,CAAE,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAC9C;AACA,CAAE,IAAI,KAAK,KAAK,GAAG,IAAE,OAAO,IAAI,GAAC;AACjC;AACA,CAAE,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,GAAC;AAC7F,CAAE,IAAI,KAAK,GAAG,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,GAAC;AAGrE;AACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB,CAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnB;AACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAClC;AACA,CAAE,OAAO,KAAK,EAAE;AAChB,EAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,EAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,EAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB;AACA,EAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC5D,EAAG;AAGH,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,gCAAW;AACZ,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;AAClE,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;AAC7B,CAAE,GAAG;AACL,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;AACtE,EAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,IAAE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;AAC5E,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;AACtE,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG;AACrC,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;AAClE,CAAE,OAAO,EAAE,CAAC;AACX,EAAC;AACF;sBACC,gCAAW;AACZ,CAAEA,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAE,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAC;AAChE,CAAEA,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;AAC7B,CAAE,GAAG;AACL,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,GAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC3C,GAAI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;AAC7E,GAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;AACpC,GAAI;AACJ;AACA,EAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,GAAI,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC7C,GAAI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;AAC/E,GAAI,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AACtC,GAAI;AACJ;AACA,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,GAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC3C,GAAI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;AAC7E,GAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;AACpC,GAAI;AACJ,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG;AACrC,CAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACxC,CAAE,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;AAC1E,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;AAC7B,EAAC;AACF;sBACC,wBAAM,KAAS,EAAE,GAA0B,EAAE;+BAAlC,GAAG;2BAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;AAAS;AAC/C,CAAE,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAClD,CAAE,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAC9C;AACA,CAAEA,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB;AACA;AACA,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B,CAAE,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;AAC/D;AACA,EAAG,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE;AAC9C,GAAI,OAAO,MAAM,CAAC;AAClB,GAAI;AACJ;AACA,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AACtB,EAAG;AACH;AACA,CAAE,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;AACpD,IAAG,MAAM,IAAI,KAAK,qCAAkC,KAAK,8BAA0B,GAAC;AACpF;AACA,CAAED,IAAM,UAAU,GAAG,KAAK,CAAC;AAC3B,CAAE,OAAO,KAAK,EAAE;AAChB,EAAG,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;AACvE,GAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;AAC1B,GAAI;AACJ;AACA,EAAGA,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;AAC7D,EAAG,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;AACvD,KAAI,MAAM,IAAI,KAAK,qCAAkC,GAAG,4BAAwB,GAAC;AACjF;AACA,EAAGA,IAAM,UAAU,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AACrE,EAAGA,IAAM,QAAQ,GAAG,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAChG;AACA,EAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACvD;AACA,EAAG,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;AAC3D,GAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;AAC1B,GAAI;AACJ;AACA,EAAG,IAAI,WAAW,EAAE;AACpB,GAAI,MAAM;AACV,GAAI;AACJ;AACA,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AACtB,EAAG;AACH;AACA,CAAE,OAAO,MAAM,CAAC;AACf,EAAC;AACF;AACC;sBACA,sBAAK,KAAK,EAAE,GAAG,EAAE;AAClB,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AAC7B,CAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACzB,CAAE,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3C;AACA,CAAE,OAAO,KAAK,CAAC;AACd,EAAC;AACF;sBACC,0BAAO,KAAK,EAAE;AACf,CAAE,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAE,SAAO;AAGvD;AACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACrC,CAAED,IAAM,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;AAC1C;AACA,CAAE,OAAO,KAAK,EAAE;AAChB,EAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAC;AACpE;AACA,EAAG,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC7E,EAAG;AACF,EAAC;AACF;sBACC,oCAAY,KAAK,EAAE,KAAK,EAAE;AAC3B,CAAE,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;AAC5C;AACA,EAAGA,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;AAChD,EAAG,MAAM,IAAI,KAAK;AAClB,6DAA0D,GAAG,CAAC,KAAI,UAAI,GAAG,CAAC,OAAM,cAAO,KAAK,CAAC,SAAQ;AACrG,GAAI,CAAC;AACL,EAAG;AACH;AACA,CAAEA,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACtC;AACA,CAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC5B,CAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AACjC,CAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACtC;AACA,CAAE,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAC;AAC1D;AACA,CAAE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;AAEjC,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,gCAAW;AACZ,CAAEC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB;AACA,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B,CAAE,OAAO,KAAK,EAAE;AAChB,EAAG,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC3B,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AACtB,EAAG;AACH;AACA,CAAE,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;AACzB,EAAC;AACF;sBACC,8BAAU;AACX,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B,CAAE,GAAG;AACL,EAAG;AACH,GAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;AAC7C,IAAK,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAClD,IAAK,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC9C;AACA,KAAI,OAAO,KAAK,GAAC;AACjB,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG;AACjC,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;sBACC,4BAAS;AACV,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B,CAAEA,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,CAAE,GAAG;AACL,EAAG,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AAC5E,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG;AACjC,CAAE,OAAO,MAAM,CAAC;AACf,EAAC;AACF;sBACC,kCAAY;AACb,CAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC9B,EAAC;AACF;sBACC,sBAAK,QAAQ,EAAE;AAChB,CAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnD,EAAC;AACF;sBACC,0CAAe,QAAQ,EAAE;AAC1B,CAAED,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACpD;AACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;AACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;AAC7B;AACA,CAAE,GAAG;AACL,EAAGD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACzB,EAAGA,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACrC;AACA;AACA,EAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1B,GAAI,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAClC,IAAK,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;AACjC,IAAK;AACL;AACA,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAClC,GAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;AAChD,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;AAC5C,GAAI;AACJ;AACA,EAAG,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;AAC5B,EAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;AAC1B,EAAG,QAAQ,KAAK,EAAE;AAClB;AACA,CAAE,OAAO,KAAK,CAAC;AACd,EAAC;AACF;sBACC,4BAAQ,QAAQ,EAAE;AACnB,CAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAChC,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;sBACD,8CAAiB,QAAQ,EAAE;AAC5B,CAAEA,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACzD;AACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;AACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B;AACA,CAAE,GAAG;AACL,EAAGD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACzB,EAAGA,IAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACvC;AACA,EAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1B;AACA,GAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,GAAC;AAC9D;AACA,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAClC,GAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;AAChD,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;AAC5C,GAAI;AACJ;AACA,EAAG,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;AAC5B,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;AACtB,EAAG,QAAQ,KAAK,EAAE;AAClB;AACA,CAAE,OAAO,KAAK,CAAC;AACd,EAAC;AACF;sBACC,gCAAU,QAAQ,EAAE;AACrB,CAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAClC,CAAE,OAAO,IAAI,CAAC;AACb;;AClsBDA,IAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AACnD;IACqB,MAAM,GAC1B,eAAW,CAAC,OAAY,EAAE;kCAAP,GAAG;AAAK;AAC5B,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;AACnC,CAAE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9E,CAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACpB,CAAE,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC1B,CAAE,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;AACvC,EAAC;AACF;iBACC,gCAAU,MAAM,EAAE;AACnB,CAAE,IAAI,MAAM,YAAY,WAAW,EAAE;AACrC,EAAG,OAAO,IAAI,CAAC,SAAS,CAAC;AACzB,GAAI,OAAO,EAAE,MAAM;AACnB,GAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC7B,GAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,GAAI,CAAC,CAAC;AACN,EAAG;AACH;AACA,CAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC5C,EAAG,MAAM,IAAI,KAAK;AAClB,GAAI,sIAAsI;AAC1I,GAAI,CAAC;AACL,EAAG;AACH;AACA,CAAE,CAAC,UAAU,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC,OAAO,WAAE,MAAM,EAAK;AACzE,EAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAC;AACjF,EAAG,CAAC,CAAC;AACL;AACA,CAAE,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;AACtC;AACA,EAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,EAAG;AACH;AACA,CAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,EAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;AAC5E,GAAI,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;AAClF,GAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC7F,GAAI,MAAM;AACV,GAAIA,IAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/F,GAAI,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,YAAY,CAAC,OAAO,EAAE;AAC1D,IAAK,MAAM,IAAI,KAAK,uCAAmC,MAAM,CAAC,SAAQ,4BAAwB,CAAC;AAC/F,IAAK;AACL,GAAI;AACJ,EAAG;AACH;AACA,CAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;iBACC,0BAAO,GAAG,EAAE,OAAO,EAAE;AACtB,CAAE,IAAI,CAAC,SAAS,CAAC;AACjB,EAAG,OAAO,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC;AAChC,EAAG,SAAS,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE;AAClD,EAAG,CAAC,CAAC;AACL;AACA,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;iBACC,0BAAQ;AACT,CAAEA,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC;AAC5B,EAAG,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,EAAG,SAAS,EAAE,IAAI,CAAC,SAAS;AAC5B,EAAG,CAAC,CAAC;AACL;AACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAK;AACnC,EAAG,MAAM,CAAC,SAAS,CAAC;AACpB,GAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC7B,GAAI,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AACnC,GAAI,SAAS,EAAE,MAAM,CAAC,SAAS;AAC/B,GAAI,CAAC,CAAC;AACN,EAAG,CAAC,CAAC;AACL;AACA,CAAE,OAAO,MAAM,CAAC;AACf,EAAC;AACF;iBACC,kDAAmB,OAAY,EAAE;;mCAAP,GAAG;AAAK;AACnC,CAAEA,IAAM,KAAK,GAAG,EAAE,CAAC;AACnB,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAK;AACnC,EAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,WAAE,IAAI,EAAK;AAC7D,GAAI,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAC;AAChD,GAAI,CAAC,CAAC;AACN,EAAG,CAAC,CAAC;AACL;AACA,CAAEA,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;AACA,CAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,EAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,EAAG;AACH;AACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAK;AACtC,EAAG,IAAI,CAAC,GAAG,CAAC,EAAE;AACd,GAAI,QAAQ,CAAC,OAAO,CAACE,QAAI,CAAC,SAAS,CAAC,CAAC;AACrC,GAAI;AACJ;AACA,EAAGF,IAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,GAAGE,QAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChG,EAAGF,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;AACtC,EAAGA,IAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnD;AACA,EAAG,IAAI,WAAW,CAAC,KAAK,EAAE;AAC1B,GAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxC,GAAI;AACJ;AACA,EAAG,WAAW,CAAC,UAAU,CAAC,QAAQ,WAAE,KAAK,EAAK;AAC9C,GAAIA,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC;AACA,GAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;AAC1D;AACA,GAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACzB,IAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AACvB,KAAM,QAAQ,CAAC,OAAO;AACtB,MAAO,WAAW;AAClB,MAAO,KAAK,CAAC,OAAO;AACpB,MAAO,GAAG;AACV,MAAO,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC3D,MAAO,CAAC;AACR,KAAM,MAAM;AACZ,KAAM,QAAQ,CAAC,gBAAgB;AAC/B,MAAO,WAAW;AAClB,MAAO,KAAK;AACZ,MAAO,WAAW,CAAC,QAAQ;AAC3B,MAAO,GAAG;AACV,MAAO,WAAW,CAAC,kBAAkB;AACrC,MAAO,CAAC;AACR,KAAM;AACN,IAAK,MAAM;AACX,IAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACrC,IAAK;AACL;AACA,GAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;AAC1D,GAAI,CAAC,CAAC;AACN;AACA,EAAG,IAAI,WAAW,CAAC,KAAK,EAAE;AAC1B,GAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxC,GAAI;AACJ,EAAG,CAAC,CAAC;AACL;AACA,CAAE,OAAO;AACT,EAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;AAChE,EAAG,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAE,MAAM,EAAK;AAC/C,GAAI,OAAO,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC3F,GAAI,CAAC;AACL,EAAG,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAE,MAAM,EAAK;AACtD,GAAI,OAAO,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1D,GAAI,CAAC;AACL,SAAG,KAAK;AACR,EAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;AACzB,EAAG,CAAC;AACH,EAAC;AACF;iBACC,oCAAY,OAAO,EAAE;AACtB,CAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;AACxD,EAAC;AACF;iBACC,8CAAkB;AACnB,CAAEA,IAAM,kBAAkB,GAAG,EAAE,CAAC;AAChC;AACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAK;AACnC,EAAGA,IAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AAC9C;AACA,EAAG,IAAI,SAAS,KAAK,IAAI,IAAE,SAAO;AAClC;AACA,EAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAE,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAC;AACzE,EAAG,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACtC,EAAG,CAAC,CAAC;AACL;AACA,CAAE;AACF,EAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,CAAC,EAAK;AAClD,GAAI,OAAO,kBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;AACzD,GAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAChB,GAAI;AACH,EAAC;AACF;iBACC,0BAAO,SAAS,EAAE;;AAAC;AACpB,CAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,EAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AACtC,EAAG;AACH;AACA,CAAE,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;AACpC;AACA,CAAEC,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACrE;AACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAK;AACtC,EAAGD,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,QAAI,CAAC,SAAS,CAAC;AACxF,EAAGF,IAAM,WAAW,GAAG,eAAe,KAAK,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9E;AACA,EAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;AACpC,GAAI,OAAO,EAAE,MAAM,CAAC,qBAAqB;AACzC,gBAAI,WAAW;AACf,GAAI,CAAC,CAAC;AACN;AACA,EAAG,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;AACxD,EAAG,CAAC,CAAC;AACL;AACA,CAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,EAAG,IAAI,CAAC,KAAK;AACb,GAAI,SAAS;AACb,GAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,YAAG,KAAK,EAAE,KAAK,EAAK;AACrD,IAAK,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;AAClD,IAAK,CAAC,CAAC;AACP,EAAG;AACH;AACA,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;iBACC,4BAAQ,GAAG,EAAE;AACd,CAAE,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;AAChC,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;iBACC,gCAAW;;AAAC;AACb,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO;AAC3B,GAAI,GAAG,WAAE,MAAM,EAAE,CAAC,EAAK;AACvB,GAAIA,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,QAAI,CAAC,SAAS,CAAC;AACzF,GAAIF,IAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrE;AACA,GAAI,OAAO,GAAG,CAAC;AACf,GAAI,CAAC;AACL,GAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACb;AACA,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,EAAC;AACF;iBACC,8BAAU;AACX,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAE,OAAO,KAAK,GAAC;AAC3D,CAAE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,WAAE,MAAM,WAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,KAAE,CAAC,IAAE,OAAO,KAAK,GAAC;AAC7E,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;iBACC,4BAAS;AACV,CAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;AAC5B,YAAI,MAAM,EAAE,MAAM,WAAK,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,KAAE;AACvD,EAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AACpB,EAAG,CAAC;AACH,EAAC;AACF;iBACC,kCAAY;AACb,CAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC9B,EAAC;AACF;iBACC,sBAAK,QAAQ,EAAE;AAChB,CAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnD,EAAC;AACF;iBACC,gCAAU,QAAQ,EAAE;AACrB,CAAEA,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACzD,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C;AACA,CAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACnB,EAAGC,IAAI,MAAM,CAAC;AACd,EAAGA,IAAI,CAAC,GAAG,CAAC,CAAC;AACb;AACA,EAAG,GAAG;AACN,GAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/B,GAAI,IAAI,CAAC,MAAM,EAAE;AACjB,IAAK,MAAM;AACX,IAAK;AACL,GAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AACxD,EAAG;AACH;AACA,CAAE,OAAO,IAAI,CAAC;AACb,EAAC;AACF;iBACC,4BAAQ,QAAQ,EAAE;AACnB,CAAED,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACpD;AACA,CAAEC,IAAI,MAAM,CAAC;AACb,CAAEA,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAClC;AACA,CAAE,GAAG;AACL,EAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9B,EAAG,IAAI,CAAC,MAAM,EAAE;AAChB,GAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC5C,GAAI,MAAM;AACV,GAAI;AACJ,EAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;AACrD;AACA,CAAE,OAAO,IAAI,CAAC;AACb;;;;"}