uni-picker-view-column {
  flex: 1;
  position: relative;
  height: 100%;
  overflow: hidden;
}

uni-picker-view-column[hidden] {
  display: none;
}

.uni-picker-view-group {
  height: 100%;
  overflow: hidden;
}

.uni-picker-view-mask {
  transform: translateZ(0);
}

.uni-picker-view-indicator,
.uni-picker-view-mask {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 3;
  pointer-events: none;
}

.uni-picker-view-mask {
  top: 0;
  height: 100%;
  margin: 0 auto;
  background-image: linear-gradient(
      180deg,
      hsla(0, 0%, 100%, 0.95),
      hsla(0, 0%, 100%, 0.6)
    ),
    linear-gradient(0deg, hsla(0, 0%, 100%, 0.95), hsla(0, 0%, 100%, 0.6));
  background-position: top, bottom;
  background-size: 100% 102px;
  background-repeat: no-repeat;
  transform: translateZ(0);
}

.uni-picker-view-indicator {
  height: 34px;
  /* top: 102px; */
  top: 50%;
  transform: translateY(-50%);
}

.uni-picker-view-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  will-change: transform;
  padding: 102px 0;
  cursor: pointer;
}

.uni-picker-view-content > * {
  height: 34px;
  overflow: hidden;
}

.uni-picker-view-indicator:after,
.uni-picker-view-indicator:before {
  content: ' ';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: #e5e5e5;
}

.uni-picker-view-indicator:before {
  top: 0;
  border-top: 1px solid #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.uni-picker-view-indicator:after {
  bottom: 0;
  border-bottom: 1px solid #e5e5e5;
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

.uni-picker-view-indicator:after,
.uni-picker-view-indicator:before {
  content: ' ';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: #e5e5e5;
}

@media (prefers-color-scheme: dark) {
  .uni-picker-view-indicator:before {
    border-top-color: var(--UI-FG-3);
  }
  .uni-picker-view-indicator:after {
    border-bottom-color: var(--UI-FG-3);
  }
  .uni-picker-view-mask {
    background-image: linear-gradient(
        180deg,
        rgba(35, 35, 35, 0.95),
        rgba(35, 35, 35, 0.6)
      ),
      linear-gradient(0deg, rgba(35, 35, 35, 0.95), rgba(35, 35, 35, 0.6));
  }
}
